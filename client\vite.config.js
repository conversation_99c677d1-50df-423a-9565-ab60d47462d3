import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import fs from 'fs'

// Custom environment loader that excludes NODE_ENV to avoid Vite warnings
function loadCustomEnv(mode, envDir) {
  const envFiles = [
    `.env.${mode}.local`,
    `.env.local`,
    `.env.${mode}`,
    `.env`
  ]

  const env = {}

  for (const file of envFiles) {
    const envPath = path.resolve(envDir, file)
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf-8')
      const lines = envContent.split('\n')

      for (const line of lines) {
        const trimmed = line.trim()
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=')
          if (key && key !== 'NODE_ENV') { // Exclude NODE_ENV
            const value = valueParts.join('=').replace(/^["']|["']$/g, '')
            env[key] = value
          }
        }
      }
    }
  }

  return env
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables from root directory (one level up from client)
  const env = loadCustomEnv(mode, path.resolve(__dirname, '../'))

  // Fallback to process.env for Docker builds where build args are passed as env vars
  const getEnvVar = (key, fallback = '') => {
    return env[key] || process.env[key] || fallback
  }

  // Determine API URL based on environment
  const getApiUrl = () => {
    // In Docker builds, use the build arg or env var
    if (process.env.REACT_APP_API_URL) {
      return process.env.REACT_APP_API_URL
    }

    // From .env file
    if (env.REACT_APP_API_URL) {
      return env.REACT_APP_API_URL
    }

    // Development fallback
    if (mode === 'development') {
      return `http://localhost:${getEnvVar('BACKEND_PORT', '8080')}/api`
    }

    // Production fallback
    return '/api'
  }

  const apiUrl = getApiUrl()
  const isDevelopment = mode === 'development'
  const isProduction = mode === 'production'

  console.log(`🔧 Vite Config - Mode: ${mode}`)
  console.log(`🔧 API URL: ${apiUrl}`)
  console.log(`🔧 Environment: ${isDevelopment ? 'Development' : 'Production'}`)

  return {
    plugins: [react()],

    // Define environment variables for the client
    define: {
      'process.env.REACT_APP_API_URL': JSON.stringify(apiUrl),
      'process.env.NODE_ENV': JSON.stringify(mode),
    },

    // Build configuration
    build: {
      outDir: 'dist',
      sourcemap: isDevelopment,
      minify: isProduction ? 'terser' : false,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            antd: ['antd', '@ant-design/icons'],
            router: ['react-router-dom'],
            utils: ['axios', 'dayjs', 'i18next', 'react-i18next'],
          },
        },
      },
      // Optimize chunk size
      chunkSizeWarningLimit: 1000,
      // Ensure assets are properly handled
      assetsDir: 'assets',
      // Generate manifest for production
      manifest: isProduction,
      // Terser options for better minification
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      } : undefined,
    },

    // Optimization
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'antd',
        'react-router-dom',
        'axios',
        'dayjs',
        'i18next',
        'react-i18next'
      ],
    },

    // Development server configuration
    server: {
      port: parseInt(getEnvVar('FRONTEND_DEV_PORT', '3000')),
      host: getEnvVar('DEV_SERVER_HOST', '0.0.0.0'),
      strictPort: true,
      open: false, // Don't auto-open browser in Docker
      // Proxy API requests to backend during development
      proxy: {
        '/api': {
          target: getEnvVar('DEV_PROXY_TARGET') || `http://localhost:${getEnvVar('BACKEND_PORT', '8080')}`,
          changeOrigin: true,
          secure: false,
          ws: true, // Enable WebSocket proxying
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('🔴 Proxy error:', err)
            })
            proxy.on('proxyReq', (_proxyReq, req, _res) => {
              console.log('🔄 Proxying request:', req.method, req.url)
            })
          }
        }
      }
    },

    // Preview server configuration (for production preview)
    preview: {
      port: parseInt(getEnvVar('FRONTEND_PORT', '8080')),
      host: getEnvVar('DEV_SERVER_HOST', '0.0.0.0'),
      strictPort: true
    },

    // Base path configuration
    base: '/',

    // CSS configuration
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          modifyVars: {
            // Ant Design theme customization can go here
          },
        },
      },
    },

    // Resolve configuration
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
  }
})