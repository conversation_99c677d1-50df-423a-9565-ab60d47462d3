{"name": "training-system-client", "version": "1.0.0", "description": "Frontend for Internal Training System", "private": true, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "express": "^4.18.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-player": "^2.12.0", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "recharts": "^2.15.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "terser": "^5.43.0", "vite": "^6.3.5"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node server.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "clean": "rm -rf dist node_modules/.vite"}}