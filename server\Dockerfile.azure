# =============================================================================
# AZURE-OPTIMIZED BACKEND DOCKERFILE
# =============================================================================

# Use Node.js LTS alpine version for smaller size
FROM node:22-alpine AS base

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    wget \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY server/package*.json ./

# Install dependencies with optimizations
RUN npm ci --only=production --prefer-offline --no-audit --no-fund && \
    npm cache clean --force

# Copy application code
COPY server/ .

# Copy root .env file
COPY .env ./.env

# Create uploads directory and set permissions
RUN mkdir -p uploads/videos uploads/documents uploads/images && \
    chown -R nodejs:nodejs /app && \
    chmod -R 755 /app && \
    chmod -R 777 uploads

# Switch to non-root user
USER nodejs

# Expose Azure standard port
EXPOSE 8080

# Health check optimized for Azure
HEALTHCHECK --interval=30s --timeout=15s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/health || exit 1

# Use dumb-init to handle signals properly in containers
ENTRYPOINT ["dumb-init", "--"]

# Start the application with proper signal handling
CMD ["node", "index.js"]
